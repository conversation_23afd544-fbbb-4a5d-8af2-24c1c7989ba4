// Simple WebSocket test script to verify the API
const WebSocket = require('ws');

const URL = 'wss://b9c2wb2z7k.execute-api.us-east-2.amazonaws.com/pre/';

console.log('Testing WebSocket Chat API...');
console.log('Connecting to:', URL);

const ws = new WebSocket(URL);

ws.on('open', function() {
  console.log('✅ Connected to WebSocket');
  
  // Set username
  console.log('📝 Setting username...');
  ws.send(JSON.stringify({
    action: 'setName',
    name: 'TestUser_' + Math.random().toString(36).substr(2, 5)
  }));
  
  // Send a public message after a short delay
  setTimeout(() => {
    console.log('📢 Sending public message...');
    ws.send(JSON.stringify({
      action: 'sendPublic',
      message: 'Hello from test script!'
    }));
  }, 1000);
  
  // Close connection after 5 seconds
  setTimeout(() => {
    console.log('👋 Closing connection...');
    ws.close();
  }, 5000);
});

ws.on('message', function(data) {
  try {
    const message = JSON.parse(data);
    console.log('📨 Received:', message);
    
    if (message.members) {
      console.log('👥 Members updated:', message.members);
    }
    if (message.systemMessage) {
      console.log('🔔 System:', message.systemMessage);
    }
    if (message.publicMessage) {
      console.log('💬 Public:', message.publicMessage);
    }
    if (message.privateMessage) {
      console.log('🔒 Private:', message.privateMessage);
    }
  } catch (error) {
    console.error('❌ Error parsing message:', error);
    console.log('Raw data:', data.toString());
  }
});

ws.on('close', function(code, reason) {
  console.log('🔌 Connection closed:', code, reason.toString());
});

ws.on('error', function(error) {
  console.error('❌ WebSocket error:', error);
});
