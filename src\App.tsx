import React, { useState, useEffect, useCallback, useRef } from 'react'
import { ChatClient } from './chat-client';

const URL = 'wss://b9c2wb2z7k.execute-api.us-east-2.amazonaws.com/pre/';

interface ChatMessage {
  id: string;
  type: 'public' | 'private' | 'system';
  content: string;
  timestamp: Date;
  from?: string;
}

const App = () => {
  const socket = useRef<WebSocket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [members, setMembers] = useState<string[]>([]);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [currentUser, setCurrentUser] = useState<string>('');
  const [connectionError, setConnectionError] = useState<string>('');

  const addMessage = useCallback((type: ChatMessage['type'], content: string, from?: string) => {
    const message: ChatMessage = {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      type,
      content,
      timestamp: new Date(),
      from
    };
    setMessages(prev => [...prev, message]);
  }, []);

  const onSocketOpen = useCallback(() => {
    console.log('WebSocket connected');
    setIsConnected(true);
    setIsConnecting(false);
    setConnectionError('');

    // Prompt for username and send setName action
    const name = prompt('Enter your name');
    if (name && name.trim()) {
      setCurrentUser(name.trim());
      try {
        socket.current?.send(JSON.stringify({
          action: 'setName',
          name: name.trim()
        }));
      } catch (error) {
        console.error('Error sending setName:', error);
        addMessage('system', 'Error setting username');
      }
    } else {
      // If no name provided, disconnect
      socket.current?.close();
    }
  }, [addMessage]);

  const onSocketClose = useCallback((event: CloseEvent) => {
    console.log('WebSocket disconnected:', event.code, event.reason);
    setMembers([]);
    setIsConnected(false);
    setIsConnecting(false);
    setCurrentUser('');

    if (event.code !== 1000) { // Not a normal closure
      setConnectionError(`Connection lost: ${event.reason || 'Unknown error'}`);
      addMessage('system', `Connection lost: ${event.reason || 'Unknown error'}`);
    } else {
      addMessage('system', 'Disconnected from chat');
    }
  }, [addMessage]);

  const onSocketError = useCallback((error: Event) => {
    console.error('WebSocket error:', error);
    setConnectionError('Connection error occurred');
    setIsConnecting(false);
    addMessage('system', 'Connection error occurred');
  }, [addMessage]);

  const onSocketMessage = useCallback((dataStr: string) => {
    try {
      const data = JSON.parse(dataStr);
      console.log('Received message:', data);

      if (data.members) {
        setMembers(data.members);
        addMessage('system', `Online members: ${data.members.join(', ')}`);
      } else if (data.publicMessage) {
        addMessage('public', data.publicMessage);
      } else if (data.privateMessage) {
        addMessage('private', data.privateMessage);
      } else if (data.systemMessage) {
        addMessage('system', data.systemMessage);
      } else {
        console.warn('Unknown message format:', data);
      }
    } catch (error) {
      console.error('Error parsing message:', error);
      addMessage('system', 'Error parsing incoming message');
    }
  }, [addMessage]);

  const onConnect = useCallback(() => {
    if (socket.current?.readyState !== WebSocket.OPEN && !isConnecting) {
      setIsConnecting(true);
      setConnectionError('');

      try {
        socket.current = new WebSocket(URL);

        socket.current.addEventListener('open', onSocketOpen);
        socket.current.addEventListener('close', onSocketClose);
        socket.current.addEventListener('error', onSocketError);
        socket.current.addEventListener('message', (event) => {
          onSocketMessage(event.data);
        });

        addMessage('system', 'Connecting to chat...');
      } catch (error) {
        console.error('Error creating WebSocket:', error);
        setIsConnecting(false);
        setConnectionError('Failed to connect');
        addMessage('system', 'Failed to connect to chat');
      }
    }
  }, [isConnecting, onSocketOpen, onSocketClose, onSocketError, onSocketMessage, addMessage]);

  useEffect(() => {
    return () => {
      socket.current?.close();
    };
  }, []);

  const onSendPrivateMessage = useCallback((to: string) => {
    if (!isConnected || !socket.current) {
      addMessage('system', 'Not connected to chat');
      return;
    }

    const message = prompt('Enter private message for ' + to);
    if (message && message.trim()) {
      try {
        socket.current.send(JSON.stringify({
          action: 'sendPrivate',
          message: message.trim(),
          to,
        }));
        addMessage('private', `To ${to}: ${message.trim()}`, currentUser);
      } catch (error) {
        console.error('Error sending private message:', error);
        addMessage('system', 'Failed to send private message');
      }
    }
  }, [isConnected, currentUser, addMessage]);

  const onSendPublicMessage = useCallback(() => {
    if (!isConnected || !socket.current) {
      addMessage('system', 'Not connected to chat');
      return;
    }

    const message = prompt('Enter public message');
    if (message && message.trim()) {
      try {
        socket.current.send(JSON.stringify({
          action: 'sendPublic',
          message: message.trim(),
        }));
      } catch (error) {
        console.error('Error sending public message:', error);
        addMessage('system', 'Failed to send public message');
      }
    }
  }, [isConnected, addMessage]);

  const onDisconnect = useCallback(() => {
    if (isConnected && socket.current) {
      socket.current.close(1000, 'User disconnected');
      addMessage('system', 'Disconnecting...');
    }
  }, [isConnected, addMessage]);

  return <ChatClient
    isConnected={isConnected}
    isConnecting={isConnecting}
    members={members}
    messages={messages}
    currentUser={currentUser}
    connectionError={connectionError}
    onPublicMessage={onSendPublicMessage}
    onPrivateMessage={onSendPrivateMessage}
    onConnect={onConnect}
    onDisconnect={onDisconnect}
  />;

}

export default App