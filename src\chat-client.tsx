import React from 'react'
import { <PERSON><PERSON>, Typo<PERSON>, Chip } from '@material-ui/core';
import ListItemText from '@material-ui/core/ListItemText';
import CssBaseline from '@material-ui/core/CssBaseline';
import Container from '@material-ui/core/Container';
import Grid from '@material-ui/core/Grid';
import List from '@material-ui/core/List';
import ListItem from '@material-ui/core/ListItem';
import Paper from '@material-ui/core/Paper';
import CircularProgress from '@material-ui/core/CircularProgress';

interface ChatMessage {
  id: string;
  type: 'public' | 'private' | 'system';
  content: string;
  timestamp: Date;
  from?: string;
}

interface Props {
  isConnected: boolean;
  isConnecting: boolean;
  members: string[];
  messages: ChatMessage[];
  currentUser: string;
  connectionError: string;
  onPublicMessage: () => void;
  onPrivateMessage: (to: string) => void;
  onConnect: () => void;
  onDisconnect: () => void;
}

const formatMessage = (message: ChatMessage) => {
  const timeStr = message.timestamp.toLocaleTimeString();

  switch (message.type) {
    case 'public':
      return (
        <span>
          <span style={{ color: '#666', fontSize: '0.8em' }}>[{timeStr}] </span>
          <strong>{message.content}</strong>
        </span>
      );
    case 'private':
      return (
        <span>
          <span style={{ color: '#666', fontSize: '0.8em' }}>[{timeStr}] </span>
          <span style={{ color: '#9c27b0', fontWeight: 'bold' }}>Private: {message.content}</span>
        </span>
      );
    case 'system':
      return (
        <span>
          <span style={{ color: '#666', fontSize: '0.8em' }}>[{timeStr}] </span>
          <em style={{ color: '#666' }}>{message.content}</em>
        </span>
      );
    default:
      return <span>{message.content}</span>;
  }
};

export const ChatClient = (props: Props) => {
  const getConnectionStatus = () => {
    if (props.isConnecting) {
      return { color: '#ff9800', text: 'Connecting...' };
    } else if (props.isConnected) {
      return { color: '#4caf50', text: 'Connected' };
    } else {
      return { color: '#f44336', text: 'Disconnected' };
    }
  };

  const status = getConnectionStatus();

  return (
    <div style={{
      position: 'absolute',
      width: '100%',
      height: '100%',
      backgroundColor: '#f4ede3',
      display: 'flex',
      alignItems: 'center',
    }}>
      <CssBaseline />
      <Container maxWidth="lg" style={{ height: '90%' }}>
        <Grid container style={{ height: '100%' }}>
          {/* Members sidebar */}
          <Grid item xs={3} style={{ backgroundColor: '#3e103f', color: 'white' }}>
            <div style={{ padding: '16px', borderBottom: '1px solid #5e205f' }}>
              <Typography variant="h6" style={{ color: 'white', marginBottom: '8px' }}>
                Online Members ({props.members.length})
              </Typography>
              {props.currentUser && (
                <Chip
                  label={`You: ${props.currentUser}`}
                  size="small"
                  style={{ backgroundColor: '#9c27b0', color: 'white' }}
                />
              )}
            </div>
            <List component="nav" style={{ padding: 0 }}>
              {props.members.map(member =>
                <ListItem
                  key={member}
                  onClick={() => {
                    if (member !== props.currentUser) {
                      props.onPrivateMessage(member);
                    }
                  }}
                  button={member !== props.currentUser}
                  style={{
                    backgroundColor: member === props.currentUser ? '#5e205f' : 'transparent',
                    cursor: member === props.currentUser ? 'default' : 'pointer'
                  }}
                >
                  <ListItemText
                    style={{ fontWeight: member === props.currentUser ? 'bold' : 'normal' }}
                    primary={member === props.currentUser ? `${member} (You)` : member}
                  />
                </ListItem>
              )}
            </List>
          </Grid>

          {/* Chat area */}
          <Grid style={{ position: 'relative' }} item container direction="column" xs={9}>
            <Paper style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
              {/* Header */}
              <div style={{
                padding: '16px',
                borderBottom: '1px solid #e0e0e0',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}>
                <Typography variant="h6">Chat Room</Typography>
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <div style={{
                    width: 12,
                    height: 12,
                    backgroundColor: status.color,
                    borderRadius: '50%',
                  }} />
                  <Typography variant="body2" style={{ color: status.color }}>
                    {status.text}
                  </Typography>
                  {props.isConnecting && <CircularProgress size={16} />}
                </div>
              </div>

              {/* Error message */}
              {props.connectionError && (
                <div style={{ padding: '8px 16px', backgroundColor: '#ffebee' }}>
                  <Typography variant="body2" style={{ color: '#d32f2f' }}>
                    {props.connectionError}
                  </Typography>
                </div>
              )}

              {/* Messages */}
              <div style={{
                flex: 1,
                overflow: 'auto',
                padding: '16px',
                maxHeight: 'calc(100vh - 300px)'
              }}>
                {props.messages.length === 0 ? (
                  <Typography variant="body2" style={{ color: '#666', textAlign: 'center', marginTop: '20px' }}>
                    {props.isConnected ? 'No messages yet. Start chatting!' : 'Connect to start chatting'}
                  </Typography>
                ) : (
                  <ul style={{
                    listStyleType: 'none',
                    padding: 0,
                    margin: 0,
                  }}>
                    {props.messages.map((message) =>
                      <li key={message.id} style={{
                        paddingBottom: '12px',
                        borderBottom: '1px solid #f5f5f5',
                        marginBottom: '8px'
                      }}>
                        {formatMessage(message)}
                      </li>
                    )}
                  </ul>
                )}
              </div>

              {/* Controls */}
              <div style={{
                padding: '16px',
                borderTop: '1px solid #e0e0e0',
                display: 'flex',
                gap: '8px',
                flexWrap: 'wrap'
              }}>
                {props.isConnected && (
                  <>
                    <Button
                      variant="contained"
                      color="primary"
                      size="small"
                      onClick={props.onPublicMessage}
                    >
                      Send Public Message
                    </Button>
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={props.onDisconnect}
                    >
                      Disconnect
                    </Button>
                  </>
                )}
                {!props.isConnected && !props.isConnecting && (
                  <Button
                    variant="contained"
                    color="primary"
                    size="small"
                    onClick={props.onConnect}
                  >
                    Connect
                  </Button>
                )}
                {props.isConnecting && (
                  <Button
                    variant="outlined"
                    size="small"
                    disabled
                  >
                    Connecting...
                  </Button>
                )}
              </div>
            </Paper>
          </Grid>
        </Grid>
      </Container>
    </div>
  )
};